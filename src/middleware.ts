import { NextRequest, NextResponse } from 'next/server';
import {
  isUserAuthenticated,
  getRouteType,
  ROUTE_PATTERNS
} from '@/lib/auth';

/**
 * Helper function to create redirect response with proper error handling
 */
function createRedirect(url: string, request: NextRequest): NextResponse {
  try {
    const redirectUrl = new URL(url, request.url);

    // Prevent redirect loops by checking if we're already at the target
    if (request.nextUrl.pathname === redirectUrl.pathname) {
      console.warn(`Prevented redirect loop to ${url}`);
      return NextResponse.next();
    }

    return NextResponse.redirect(redirectUrl);
  } catch (error) {
    console.error('Error creating redirect:', error);
    // Fallback to allowing the request to continue
    return NextResponse.next();
  }
}

/**
 * Main middleware function
 */
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Determine route type
  const routeType = getRouteType(pathname);

  // Skip middleware for API routes, static files, and Next.js internals
  if (routeType === 'api' || routeType === 'static') {
    return NextResponse.next();
  }

  // Additional check for file extensions and Next.js internals
  if (pathname.includes('.') && !pathname.endsWith('/')) {
    return NextResponse.next();
  }

  // Check authentication status
  const authResult = isUserAuthenticated(request);
  const { isAuthenticated, error } = authResult;

  // Log authentication errors for debugging (in development)
  if (error ) {
    console.log(`Auth check for ${pathname}:`, { isAuthenticated, error });
  }

  // Handle root path redirect
  if (pathname === '/') {
    if (isAuthenticated) {
      return createRedirect('/kanban', request);
    } else {
      return createRedirect('/login', request);
    }
  }

  // Handle protected routes
  if (routeType === 'protected') {
    if (!isAuthenticated) {
      // Add the original URL as a query parameter for post-login redirect
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }
    return NextResponse.next();
  }

  // Handle public routes (login, register)
  if (routeType === 'public') {
    if (isAuthenticated) {
      // Check if there's a redirect parameter
      const redirectParam = request.nextUrl.searchParams.get('redirect');
      const redirectTo = redirectParam && redirectParam.startsWith('/') ? redirectParam : '/kanban';
      return createRedirect(redirectTo, request);
    }
    return NextResponse.next();
  }

  // For any other routes, allow access
  return NextResponse.next();
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (images, etc.)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.).*)',
  ],
};
