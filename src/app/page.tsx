'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getCookie } from 'cookies-next';
import { verifyJwt } from '@/lib/jwt';

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // This component should rarely be seen due to middleware redirects,
    // but we'll add client-side redirect as a fallback
    const checkAuthAndRedirect = async () => {
      try {
        const token = getCookie('access_token');

        if (token && typeof token === 'string') {
          // Verify token is valid
          const payload = verifyJwt(token);
          if (payload) {
            // Valid token, redirect to kanban
            router.replace('/kanban');
            return;
          }
        }

        // No valid token, redirect to login
        router.replace('/login');
      } catch (error) {
        console.error('Error checking authentication:', error);
        // On error, redirect to login
        router.replace('/login');
      }
    };

    checkAuthAndRedirect();
  }, [router]);

  // Show loading state while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting...</p>
      </div>
    </div>
  );
}
