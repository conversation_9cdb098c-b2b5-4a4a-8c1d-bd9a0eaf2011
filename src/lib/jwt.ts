import jwt from 'jsonwebtoken';

// JWT secret key - in production, this should be stored in environment variables
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here';

export interface JwtPayload {
  userId: number;
  email: string;
  role: string;
}

export function signJwt(payload: JwtPayload): string {
  // @ts-ignore - Ignoring TypeScript errors for now
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  });
}

export function verifyJwt(token: string): JwtPayload | null {
  try {
    // @ts-ignore - Ignoring TypeScript errors for now    
    return jwt.verify(token, JWT_SECRET) as JwtPayload;
  } catch (error) {
    return null;
  }
}
