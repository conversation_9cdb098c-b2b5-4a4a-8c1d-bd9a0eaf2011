import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

/**
 * Helper function to extract token from request
 */
export function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

/**
 * Helper function to verify user is authenticated and get user info
 */
export async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
  };
}

/**
 * Simplified authentication for endpoints that only need userId
 */
export async function authenticateUserSimple(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  return { userId: payload.userId };
}

/**
 * Middleware-specific authentication utilities
 */

/**
 * Parse cookies from cookie header string
 */
export function parseCookies(cookieHeader: string | null): Record<string, string> {
  if (!cookieHeader) return {};

  try {
    return cookieHeader.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      if (key && value) {
        acc[key] = decodeURIComponent(value);
      }
      return acc;
    }, {} as Record<string, string>);
  } catch (error) {
    console.error('Error parsing cookies:', error);
    return {};
  }
}

/**
 * Get access token from request cookies
 */
export function getAccessTokenFromCookies(request: NextRequest): string | null {
  try {
    const cookieHeader = request.headers.get('cookie');
    const cookies = parseCookies(cookieHeader);
    return cookies.access_token || null;
  } catch (error) {
    console.error('Error getting access token from cookies:', error);
    return null;
  }
}

/**
 * Validate token and return payload if valid
 */
export function validateAccessToken(token: string) {
  try {
    if (!token || token.trim() === '') {
      return { isValid: false, payload: null, error: 'Token is empty' };
    }

    const payload = verifyJwt(token);

    if (!payload) {
      return { isValid: false, payload: null, error: 'Invalid or expired token' };
    }

    return { isValid: true, payload, error: null };
  } catch (error) {
    console.error('Token validation error:', error);
    return {
      isValid: false,
      payload: null,
      error: error instanceof Error ? error.message : 'Token validation failed'
    };
  }
}

/**
 * Check if user is authenticated based on request
 */
export function isUserAuthenticated(request: NextRequest): {
  isAuthenticated: boolean;
  token: string | null;
  payload: any;
  error: string | null;
} {
  const token = getAccessTokenFromCookies(request);

  if (!token) {
    return {
      isAuthenticated: false,
      token: null,
      payload: null,
      error: 'No access token found'
    };
  }

  const validation = validateAccessToken(token);

  return {
    isAuthenticated: validation.isValid,
    token,
    payload: validation.payload,
    error: validation.error
  };
}

/**
 * Route protection utilities
 */
export const ROUTE_PATTERNS = {
  PROTECTED: [
    '/kanban',
    '/chat',
    '/notification',
    '/overview',
    '/ranking',
    '/settings',
  ],
  PUBLIC: [
    '/login',
    '/register',
  ],
  API: [
    '/api',
  ],
  STATIC: [
    '/_next',
    '/favicon.ico',
    '/public',
  ]
};

/**
 * Check if pathname matches any of the given patterns
 */
export function matchesRoutePattern(pathname: string, patterns: string[]): boolean {
  return patterns.some(pattern => {
    // Handle wildcard patterns
    if (pattern.endsWith('*')) {
      return pathname.startsWith(pattern.slice(0, -1));
    }
    // Exact match or starts with pattern followed by /
    return pathname === pattern || pathname.startsWith(pattern + '/');
  });
}

/**
 * Determine route type based on pathname
 */
export function getRouteType(pathname: string): 'protected' | 'public' | 'api' | 'static' | 'other' {
  if (matchesRoutePattern(pathname, ROUTE_PATTERNS.API)) return 'api';
  if (matchesRoutePattern(pathname, ROUTE_PATTERNS.STATIC)) return 'static';
  if (matchesRoutePattern(pathname, ROUTE_PATTERNS.PROTECTED)) return 'protected';
  if (matchesRoutePattern(pathname, ROUTE_PATTERNS.PUBLIC)) return 'public';
  return 'other';
}
